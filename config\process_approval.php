<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Models Path
    |--------------------------------------------------------------------------
    |
    | The path where your approvable models are located.
    | This is used to scan for models that extend ApprovableModel.
    |
    */
    'models_path' => 'App\\Models',

    /*
    |--------------------------------------------------------------------------
    | Roles Model
    |--------------------------------------------------------------------------
    |
    | The model that represents roles in your application.
    | This should be compatible with spatie/laravel-permission package.
    |
    */
    'roles_model' => App\Models\Role::class,

    /*
    |--------------------------------------------------------------------------
    | User Model
    |--------------------------------------------------------------------------
    |
    | The model that represents users in your application.
    |
    */
    'user_model' => App\Models\User::class,

    /*
    |--------------------------------------------------------------------------
    | Approval Flow Model
    |--------------------------------------------------------------------------
    |
    | The model that represents approval flows.
    |
    */
    'approval_flow_model' => App\Models\ProcessApprovalFlow::class,

    /*
    |--------------------------------------------------------------------------
    | Approval Step Model
    |--------------------------------------------------------------------------
    |
    | The model that represents approval steps.
    |
    */
    'approval_step_model' => App\Models\ProcessApprovalFlowStep::class,

    /*
    |--------------------------------------------------------------------------
    | Tenancy Settings
    |--------------------------------------------------------------------------
    |
    | Configure tenancy settings for approval processes.
    | Set to false to disable multi-tenancy features.
    |
    */
    'tenancy' => [
        'enabled' => false,
        'tenant_model' => null,
        'tenant_foreign_key' => null,
    ],
];
